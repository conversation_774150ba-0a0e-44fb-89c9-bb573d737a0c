{% extends "layout/base.html" %}

{% block title %}إضافة موعد جديد - {{ app_name }}{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card shadow">
            <div class="card-header bg-primary text-white">
                <h3 class="mb-0">إضافة موعد جديد</h3>
            </div>
            <div class="card-body">
                <form method="POST" action="{{ url_for('appointments.add_appointment') }}">
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="title" class="form-label">عنوان الموعد</label>
                            <input type="text" class="form-control" id="title" name="title" required>
                        </div>
                        <div class="col-md-6">
                            <label for="appointment_type" class="form-label">نوع الموعد</label>
                            <select class="form-select" id="appointment_type" name="appointment_type" required>
                                <option value="" selected disabled>اختر نوع الموعد</option>
                                <option value="hearing">جلسة</option>
                                <option value="meeting">اجتماع</option>
                                <option value="reminder">تذكير</option>
                            </select>
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="date" class="form-label">التاريخ</label>
                            <input type="date" class="form-control" id="date" name="date" required>
                        </div>
                        <div class="col-md-6">
                            <label for="time" class="form-label">الوقت</label>
                            <input type="time" class="form-control" id="time" name="time" required>
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="end_date" class="form-label">تاريخ الانتهاء (اختياري)</label>
                            <input type="date" class="form-control" id="end_date" name="end_date">
                        </div>
                        <div class="col-md-6">
                            <label for="end_time" class="form-label">وقت الانتهاء (اختياري)</label>
                            <input type="time" class="form-control" id="end_time" name="end_time">
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="location" class="form-label">المكان</label>
                        <input type="text" class="form-control" id="location" name="location">
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="case_id" class="form-label">القضية</label>
                            <select class="form-select" id="case_id" name="case_id">
                                <option value="">بدون قضية</option>
                                {% for case in cases %}
                                <option value="{{ case.id }}" {% if request.args.get('case_id')|int == case.id %}selected{% endif %}>{{ case.case_number }} - {{ case.title }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label for="status" class="form-label">الحالة</label>
                            <select class="form-select" id="status" name="status" required>
                                <option value="scheduled" selected>مجدول</option>
                                <option value="completed">مكتمل</option>
                                <option value="cancelled">ملغي</option>
                                <option value="postponed">مؤجل</option>
                            </select>
                        </div>
                    </div>
                    {% if session.role == 'admin' %}
                    <div class="mb-3">
                        <label for="user_id" class="form-label">المسؤول</label>
                        <select class="form-select" id="user_id" name="user_id" required>
                            {% for user in users %}
                            <option value="{{ user.id }}" {% if user.id == session.user_id %}selected{% endif %}>{{ user.first_name }} {{ user.last_name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    {% endif %}
                    <div class="mb-3">
                        <label for="description" class="form-label">الوصف</label>
                        <textarea class="form-control" id="description" name="description" rows="3"></textarea>
                    </div>
                    <div class="mb-3">
                        <label for="notes" class="form-label">ملاحظات</label>
                        <textarea class="form-control" id="notes" name="notes" rows="3"></textarea>
                    </div>
                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-primary">إضافة الموعد</button>
                        <a href="{{ url_for('appointments.index') }}" class="btn btn-secondary">إلغاء</a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // تعيين التاريخ والوقت الحاليين
        const dateInput = document.getElementById('date');
        const timeInput = document.getElementById('time');
        
        if (!dateInput.value) {
            const now = new Date();
            const year = now.getFullYear();
            const month = String(now.getMonth() + 1).padStart(2, '0');
            const day = String(now.getDate()).padStart(2, '0');
            
            dateInput.value = `${year}-${month}-${day}`;
        }
        
        if (!timeInput.value) {
            const now = new Date();
            const hours = String(now.getHours()).padStart(2, '0');
            const minutes = String(now.getMinutes()).padStart(2, '0');
            
            timeInput.value = `${hours}:${minutes}`;
        }
        
        // التحقق من تاريخ ووقت الانتهاء
        const endDateInput = document.getElementById('end_date');
        const endTimeInput = document.getElementById('end_time');
        
        endDateInput.addEventListener('change', validateEndDateTime);
        endTimeInput.addEventListener('change', validateEndDateTime);
        dateInput.addEventListener('change', validateEndDateTime);
        timeInput.addEventListener('change', validateEndDateTime);
        
        function validateEndDateTime() {
            if (endDateInput.value && endTimeInput.value) {
                const startDate = new Date(`${dateInput.value}T${timeInput.value}`);
                const endDate = new Date(`${endDateInput.value}T${endTimeInput.value}`);
                
                if (endDate <= startDate) {
                    alert('يجب أن يكون وقت الانتهاء بعد وقت البدء');
                    endDateInput.value = dateInput.value;
                    
                    // تعيين وقت الانتهاء بعد ساعة من وقت البدء
                    const newEndTime = new Date(startDate);
                    newEndTime.setHours(newEndTime.getHours() + 1);
                    
                    const hours = String(newEndTime.getHours()).padStart(2, '0');
                    const minutes = String(newEndTime.getMinutes()).padStart(2, '0');
                    
                    endTimeInput.value = `${hours}:${minutes}`;
                }
            }
        }
    });
</script>
{% endblock %}
