{% extends "layout/base.html" %}

{% block title %}إضافة مستند جديد - {{ app_name }}{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card shadow">
            <div class="card-header bg-primary text-white">
                <h3 class="mb-0">إضافة مستند جديد</h3>
            </div>
            <div class="card-body">
                <form method="POST" action="{{ url_for('documents.add_document') }}" enctype="multipart/form-data">
                    <div class="mb-3">
                        <label for="title" class="form-label">عنوان المستند</label>
                        <input type="text" class="form-control" id="title" name="title" required>
                    </div>
                    <div class="mb-3">
                        <label for="document_type" class="form-label">نوع المستند</label>
                        <select class="form-select" id="document_type" name="document_type" required>
                            <option value="" selected disabled>اختر نوع المستند</option>
                            <option value="contract">عقد</option>
                            <option value="court_document">مستند محكمة</option>
                            <option value="id">هوية</option>
                            <option value="power_of_attorney">توكيل</option>
                            <option value="report">تقرير</option>
                            <option value="other">آخر</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="description" class="form-label">وصف المستند</label>
                        <textarea class="form-control" id="description" name="description" rows="3"></textarea>
                    </div>
                    <div class="mb-3">
                        <label for="file" class="form-label">الملف</label>
                        <input type="file" class="form-control" id="file" name="file" required>
                        <div class="form-text">الملفات المسموح بها: PDF, DOC, DOCX, JPG, JPEG, PNG (الحد الأقصى: 16 ميجابايت)</div>
                    </div>
                    
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="case_ids" class="form-label">القضايا المرتبطة (اختياري)</label>
                            <select class="form-select" id="case_ids" name="case_ids" multiple>
                                {% for case in cases %}
                                <option value="{{ case.id }}" {% if request.args.get('case_id')|int == case.id %}selected{% endif %}>{{ case.case_number }} - {{ case.title }}</option>
                                {% endfor %}
                            </select>
                            <div class="form-text">اضغط Ctrl للاختيار المتعدد</div>
                        </div>
                        <div class="col-md-6">
                            <label for="client_ids" class="form-label">العملاء المرتبطين (اختياري)</label>
                            <select class="form-select" id="client_ids" name="client_ids" multiple>
                                {% for client in clients %}
                                <option value="{{ client.id }}" {% if request.args.get('client_id')|int == client.id %}selected{% endif %}>{{ client.first_name }} {{ client.last_name }}</option>
                                {% endfor %}
                            </select>
                            <div class="form-text">اضغط Ctrl للاختيار المتعدد</div>
                        </div>
                    </div>
                    
                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-primary">رفع المستند</button>
                        <a href="{{ url_for('documents.index') }}" class="btn btn-secondary">إلغاء</a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}
