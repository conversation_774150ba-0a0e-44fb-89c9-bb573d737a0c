# نظام إدارة مكتب المحاماة

نظام متكامل لإدارة مكاتب المحاماة مبني باستخدام Python وإطار Flask مع واجهة مستخدم عربية.

## المميزات

- **إدارة القضايا**: تسجيل وتتبع القضايا، مع تفاصيل العملاء والمحكمة ونوع القضية وحالتها.
- **إدارة العملاء**: قاعدة بيانات شاملة للعملاء مع سجل كامل لقضاياهم.
- **إدارة الجلسات والمواعيد**: جدولة وتتبع المواعيد والجلسات مع تقويم متكامل.
- **إدارة الفواتير والمدفوعات**: إنشاء وتتبع الفواتير والمدفوعات المرتبطة بالقضايا والعملاء.
- **إدارة المستندات**: تخزين وتنظيم المستندات المرتبطة بالقضايا والعملاء.
- **إدارة المستخدمين والصلاحيات**: مستويات مختلفة من الصلاحيات للمستخدمين (مسؤول، محامي، موظف).
- **لوحة تحكم وتقارير**: إحصائيات وتقارير عن أداء المكتب والقضايا والإيرادات.
- **واجهة مستخدم عربية**: واجهة مستخدم كاملة باللغة العربية مع دعم الاتجاه من اليمين إلى اليسار.

## متطلبات النظام

- Python 3.8+
- Flask
- SQLAlchemy
- Flask-Login
- Flask-Migrate
- Flask-WTF
- وغيرها من المكتبات المذكورة في ملف requirements.txt

## التثبيت

1. قم بنسخ المستودع:
```
git clone https://github.com/yourusername/law-office-management.git
cd law-office-management
```

2. قم بإنشاء بيئة افتراضية وتفعيلها:
```
python -m venv venv
# في نظام Windows
venv\Scripts\activate
# في نظام Linux/Mac
source venv/bin/activate
```

3. قم بتثبيت المتطلبات:
```
pip install -r requirements.txt
```

4. قم بإعداد قاعدة البيانات وإنشاء مستخدم مسؤول:
```
python setup_db.py
```

5. قم بتشغيل التطبيق:
```
python app.py
```

6. افتح المتصفح على العنوان: `http://localhost:5000`

## بيانات تسجيل الدخول الافتراضية

- **اسم المستخدم**: admin
- **كلمة المرور**: admin123

## الهيكل التنظيمي للمشروع

```
law-office-management/
├── app.py                  # الملف الرئيسي للتطبيق
├── config.py               # إعدادات التطبيق
├── models.py               # نماذج قاعدة البيانات
├── setup_db.py             # سكريبت إعداد قاعدة البيانات
├── requirements.txt        # متطلبات المشروع
├── routes/                 # مجلد المسارات
│   ├── __init__.py
│   ├── auth.py             # مسارات المصادقة
│   ├── cases.py            # مسارات القضايا
│   ├── clients.py          # مسارات العملاء
│   ├── appointments.py     # مسارات المواعيد
│   ├── invoices.py         # مسارات الفواتير
│   ├── documents.py        # مسارات المستندات
│   └── dashboard.py        # مسارات لوحة التحكم
├── static/                 # الملفات الثابتة
│   ├── css/
│   ├── js/
│   ├── img/
│   └── uploads/            # مجلد تحميل الملفات
└── templates/              # قوالب HTML
    ├── auth/
    ├── cases/
    ├── clients/
    ├── appointments/
    ├── invoices/
    ├── documents/
    ├── dashboard/
    ├── errors/
    └── layout/
```

## المساهمة

نرحب بمساهماتكم! يرجى إنشاء fork للمشروع وإرسال طلب سحب مع التغييرات المقترحة.

## الترخيص

هذا المشروع مرخص تحت رخصة MIT. انظر ملف LICENSE للمزيد من التفاصيل.
