<!DOCTYPE html>
<html lang="ar" dir="rtl" data-bs-theme="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}{{ app_name }}{% endblock %}</title>

    <!-- Bootstrap RTL CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.rtl.min.css">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">

    <style>
        :root {
            --primary-color: #0d6efd;
            --secondary-color: #6c757d;
            --success-color: #198754;
            --info-color: #0dcaf0;
            --warning-color: #ffc107;
            --danger-color: #dc3545;
            --light-color: #f8f9fa;
            --dark-color: #212529;
            --bs-body-bg: #f8f9fa;
            --bs-body-color: #212529;
        }

        [data-bs-theme="dark"] {
            --bs-body-bg: #212529;
            --bs-body-color: #f8f9fa;
            --bs-tertiary-bg: #2b3035;
            --bs-emphasis-color: #fff;
            --bs-secondary-color: rgba(255, 255, 255, 0.75);
        }

        body {
            transition: background-color 0.3s ease, color 0.3s ease;
        }

        [data-bs-theme="dark"] .card {
            background-color: #2b3035;
            border-color: #444;
        }

        [data-bs-theme="dark"] .table {
            --bs-table-color: #f8f9fa;
            --bs-table-bg: #2b3035;
            --bs-table-border-color: #444;
        }

        [data-bs-theme="dark"] .table-striped > tbody > tr:nth-of-type(odd) > * {
            --bs-table-accent-bg: #343a40;
        }

        [data-bs-theme="dark"] .form-control,
        [data-bs-theme="dark"] .form-select {
            background-color: #343a40;
            border-color: #444;
            color: #f8f9fa;
        }

        [data-bs-theme="dark"] .input-group-text {
            background-color: #495057;
            border-color: #444;
            color: #f8f9fa;
        }

        [data-bs-theme="dark"] .modal-content {
            background-color: #2b3035;
            border-color: #444;
        }

        [data-bs-theme="dark"] .dropdown-menu {
            background-color: #343a40;
            border-color: #444;
        }

        [data-bs-theme="dark"] .dropdown-item {
            color: #f8f9fa;
        }

        [data-bs-theme="dark"] .dropdown-item:hover {
            background-color: #495057;
        }

        .theme-toggle-btn {
            cursor: pointer;
            padding: 5px 10px;
            border-radius: 50px;
            display: inline-flex;
            align-items: center;
            font-size: 1.5rem;
            margin-left: 10px;
            color: var(--bs-body-color);
            background-color: transparent;
            border: none;
        }

        .theme-toggle-btn:hover {
            opacity: 0.8;
        }

        .theme-toggle-btn i {
            transition: transform 0.3s ease;
        }
    </style>

    {% block styles %}{% endblock %}
</head>
<body>
    {% if session.user_id %}
    <!-- القائمة الجانبية -->
    <div class="container-fluid">
        <div class="row flex-nowrap">
            <div class="col-auto col-md-3 col-xl-2 px-sm-2 px-0 bg-dark">
                <div class="d-flex flex-column align-items-center align-items-sm-start px-3 pt-2 text-white min-vh-100">
                    <a href="{{ url_for('dashboard.index') }}" class="d-flex align-items-center pb-3 mb-md-0 me-md-auto text-white text-decoration-none">
                        <span class="fs-5 d-none d-sm-inline">{{ app_name }}</span>
                    </a>
                    <ul class="nav nav-pills flex-column mb-sm-auto mb-0 align-items-center align-items-sm-start" id="menu">
                        <li class="nav-item">
                            <a href="{{ url_for('dashboard.index') }}" class="nav-link align-middle px-0">
                                <i class="fs-4 fa fa-tachometer-alt"></i> <span class="ms-1 d-none d-sm-inline">لوحة التحكم</span>
                            </a>
                        </li>
                        <li>
                            <a href="{{ url_for('cases.index') }}" class="nav-link px-0 align-middle">
                                <i class="fs-4 fa fa-gavel"></i> <span class="ms-1 d-none d-sm-inline">القضايا</span>
                            </a>
                        </li>
                        <li>
                            <a href="{{ url_for('clients.index') }}" class="nav-link px-0 align-middle">
                                <i class="fs-4 fa fa-users"></i> <span class="ms-1 d-none d-sm-inline">العملاء</span>
                            </a>
                        </li>
                        <li>
                            <a href="{{ url_for('appointments.index') }}" class="nav-link px-0 align-middle">
                                <i class="fs-4 fa fa-calendar"></i> <span class="ms-1 d-none d-sm-inline">المواعيد</span>
                            </a>
                        </li>
                        <li>
                            <a href="{{ url_for('appointments.calendar') }}" class="nav-link px-0 align-middle">
                                <i class="fs-4 fa fa-calendar-alt"></i> <span class="ms-1 d-none d-sm-inline">التقويم</span>
                            </a>
                        </li>
                        <li>
                            <a href="{{ url_for('invoices.index') }}" class="nav-link px-0 align-middle">
                                <i class="fs-4 fa fa-file-invoice-dollar"></i> <span class="ms-1 d-none d-sm-inline">الفواتير</span>
                            </a>
                        </li>
                        <li>
                            <a href="{{ url_for('documents.index') }}" class="nav-link px-0 align-middle">
                                <i class="fs-4 fa fa-file-alt"></i> <span class="ms-1 d-none d-sm-inline">المستندات</span>
                            </a>
                        </li>
                        <li>
                            <a href="{{ url_for('reports.index') }}" class="nav-link px-0 align-middle">
                                <i class="fs-4 fa fa-chart-bar"></i> <span class="ms-1 d-none d-sm-inline">التقارير</span>
                            </a>
                        </li>
                        {% if session.role == 'admin' %}
                        <li>
                            <a href="{{ url_for('auth.users_list') }}" class="nav-link px-0 align-middle">
                                <i class="fs-4 fa fa-user-shield"></i> <span class="ms-1 d-none d-sm-inline">المستخدمين</span>
                            </a>
                        </li>
                        {% endif %}
                    </ul>
                    <hr>
                    <div class="dropdown pb-4">
                        <a href="#" class="d-flex align-items-center text-white text-decoration-none dropdown-toggle" id="dropdownUser1" data-bs-toggle="dropdown" aria-expanded="false">
                            <img src="https://ui-avatars.com/api/?name={{ session.first_name }}+{{ session.last_name }}&background=random" alt="Avatar" width="30" height="30" class="rounded-circle">
                            <span class="d-none d-sm-inline mx-1">{{ session.first_name }}</span>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-dark text-small shadow">
                            <li><a class="dropdown-item" href="{{ url_for('auth.profile') }}">الملف الشخصي</a></li>
                            <li>
                                <hr class="dropdown-divider">
                            </li>
                            <li><a class="dropdown-item" href="{{ url_for('auth.logout') }}">تسجيل الخروج</a></li>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="col py-3">
                <!-- المحتوى الرئيسي -->
                <div class="container">
                    <!-- زر تبديل الوضع الداكن -->
                    <div class="d-flex justify-content-end mb-3">
                        <button id="theme-toggle" class="theme-toggle-btn" title="تبديل الوضع الداكن">
                            <i class="fas fa-moon"></i>
                        </button>
                    </div>
                    {% with messages = get_flashed_messages(with_categories=true) %}
                        {% if messages %}
                            {% for category, message in messages %}
                                <div class="alert alert-{{ category }} alert-dismissible fade show" role="alert">
                                    {{ message }}
                                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                                </div>
                            {% endfor %}
                        {% endif %}
                    {% endwith %}

                    {% block content %}{% endblock %}
                </div>
            </div>
        </div>
    </div>
    {% else %}
    <!-- صفحة تسجيل الدخول -->
    <div class="container">
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ category }} alert-dismissible fade show mt-3" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}

        {% block auth_content %}{% endblock %}
    </div>
    {% endif %}

    <!-- Bootstrap JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>

    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>

    <!-- Custom JS -->
    <script src="{{ url_for('static', filename='js/main.js') }}"></script>

    <!-- Dark Mode Script -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // التحقق من وجود تفضيل الوضع الداكن في localStorage
            const currentTheme = localStorage.getItem('theme');
            const themeToggleBtn = document.getElementById('theme-toggle');
            const themeIcon = themeToggleBtn.querySelector('i');

            // تطبيق الوضع المخزن مسبقًا
            if (currentTheme) {
                document.documentElement.setAttribute('data-bs-theme', currentTheme);

                if (currentTheme === 'dark') {
                    themeIcon.classList.remove('fa-moon');
                    themeIcon.classList.add('fa-sun');
                } else {
                    themeIcon.classList.remove('fa-sun');
                    themeIcon.classList.add('fa-moon');
                }
            }

            // معالجة النقر على زر تبديل الوضع
            themeToggleBtn.addEventListener('click', function() {
                let theme = 'light';

                if (document.documentElement.getAttribute('data-bs-theme') === 'dark') {
                    document.documentElement.setAttribute('data-bs-theme', 'light');
                    themeIcon.classList.remove('fa-sun');
                    themeIcon.classList.add('fa-moon');
                } else {
                    document.documentElement.setAttribute('data-bs-theme', 'dark');
                    themeIcon.classList.remove('fa-moon');
                    themeIcon.classList.add('fa-sun');
                    theme = 'dark';
                }

                // حفظ التفضيل في localStorage
                localStorage.setItem('theme', theme);
            });
        });
    </script>

    {% block scripts %}{% endblock %}
</body>
</html>
