{% extends "layout/base.html" %}

{% block title %}إضافة فاتورة جديدة - {{ app_name }}{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card shadow">
            <div class="card-header bg-primary text-white">
                <h3 class="mb-0">إضافة فاتورة جديدة</h3>
            </div>
            <div class="card-body">
                <form method="POST" action="{{ url_for('invoices.add_invoice') }}">
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="client_id" class="form-label">العميل</label>
                            <select class="form-select" id="client_id" name="client_id" required>
                                <option value="" selected disabled>اختر العميل</option>
                                {% for client in clients %}
                                <option value="{{ client.id }}" {% if request.args.get('client_id')|int == client.id %}selected{% endif %}>{{ client.first_name }} {{ client.last_name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label for="case_id" class="form-label">القضية (اختياري)</label>
                            <select class="form-select" id="case_id" name="case_id">
                                <option value="">بدون قضية</option>
                                {% for case in cases %}
                                <option value="{{ case.id }}" {% if request.args.get('case_id')|int == case.id %}selected{% endif %}>{{ case.case_number }} - {{ case.title }}</option>
                                {% endfor %}
                            </select>
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="amount" class="form-label">المبلغ</label>
                            <div class="input-group">
                                <input type="number" class="form-control" id="amount" name="amount" step="0.01" min="0" required>
                                <span class="input-group-text">دينار</span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <label for="due_date" class="form-label">تاريخ الاستحقاق</label>
                            <input type="date" class="form-control" id="due_date" name="due_date" required>
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-12">
                            <label for="status" class="form-label">حالة الفاتورة</label>
                            <select class="form-select" id="status" name="status" required>
                                <option value="unpaid" selected>غير مدفوعة</option>
                                <option value="paid">مدفوعة</option>
                                <option value="partial">مدفوعة جزئياً</option>
                                <option value="cancelled">ملغاة</option>
                            </select>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="description" class="form-label">الوصف</label>
                        <textarea class="form-control" id="description" name="description" rows="3"></textarea>
                    </div>
                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-primary">إضافة الفاتورة</button>
                        <a href="{{ url_for('invoices.index') }}" class="btn btn-secondary">إلغاء</a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // تعيين تاريخ الاستحقاق افتراضياً بعد 30 يوماً
        const dueDateInput = document.getElementById('due_date');
        if (!dueDateInput.value) {
            const today = new Date();
            const dueDate = new Date();
            dueDate.setDate(today.getDate() + 30);

            const year = dueDate.getFullYear();
            const month = String(dueDate.getMonth() + 1).padStart(2, '0');
            const day = String(dueDate.getDate()).padStart(2, '0');

            dueDateInput.value = `${year}-${month}-${day}`;
        }
    });
</script>
{% endblock %}
