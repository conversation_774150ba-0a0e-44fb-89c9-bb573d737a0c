{% extends "layout/base.html" %}

{% block title %}إضافة قضية جديدة - {{ app_name }}{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-10">
        <div class="card shadow">
            <div class="card-header bg-primary text-white">
                <h3 class="mb-0">إضافة قضية جديدة</h3>
            </div>
            <div class="card-body">
                <form method="POST" action="{{ url_for('cases.add_case') }}">
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="case_number" class="form-label">رقم القضية</label>
                            <input type="text" class="form-control" id="case_number" name="case_number" required>
                        </div>
                        <div class="col-md-6">
                            <label for="title" class="form-label">عنوان القضية</label>
                            <input type="text" class="form-control" id="title" name="title" required>
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="case_type" class="form-label">نوع القضية</label>
                            <select class="form-select" id="case_type" name="case_type" required>
                                <option value="" selected disabled>اختر نوع القضية</option>
                                <option value="مدني">مدني</option>
                                <option value="جنائي">جنائي</option>
                                <option value="تجاري">تجاري</option>
                                <option value="عمالي">عمالي</option>
                                <option value="أحوال شخصية">أحوال شخصية</option>
                                <option value="إداري">إداري</option>
                                <option value="عقاري">عقاري</option>
                                <option value="آخر">آخر</option>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label for="court" class="form-label">المحكمة</label>
                            <input type="text" class="form-control" id="court" name="court">
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="client_id" class="form-label">العميل</label>
                            <select class="form-select" id="client_id" name="client_id" required>
                                <option value="" selected disabled>اختر العميل</option>
                                {% for client in clients %}
                                <option value="{{ client.id }}">{{ client.first_name }} {{ client.last_name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label for="lawyer_id" class="form-label">المحامي المسؤول</label>
                            <select class="form-select" id="lawyer_id" name="lawyer_id" required>
                                <option value="" selected disabled>اختر المحامي</option>
                                {% for lawyer in lawyers %}
                                <option value="{{ lawyer.id }}">{{ lawyer.first_name }} {{ lawyer.last_name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="opponent_name" class="form-label">اسم الخصم</label>
                            <input type="text" class="form-control" id="opponent_name" name="opponent_name">
                        </div>
                        <div class="col-md-6">
                            <label for="status" class="form-label">حالة القضية</label>
                            <select class="form-select" id="status" name="status" required>
                                <option value="open" selected>مفتوحة</option>
                                <option value="pending">معلقة</option>
                                <option value="postponed">مؤجلة</option>
                                <option value="closed">مغلقة</option>
                            </select>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="description" class="form-label">وصف القضية</label>
                        <textarea class="form-control" id="description" name="description" rows="5"></textarea>
                    </div>
                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-primary">إضافة القضية</button>
                        <a href="{{ url_for('cases.index') }}" class="btn btn-secondary">إلغاء</a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}
