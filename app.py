import os
from flask import Flask, render_template, redirect, url_for, flash, session
from werkzeug.utils import secure_filename
from datetime import datetime

from config import Config
from models import db, User

# استيراد المسارات
from routes.auth import auth_bp
from routes.cases import cases_bp
from routes.clients import clients_bp
from routes.appointments import appointments_bp
from routes.invoices import invoices_bp
from routes.documents import documents_bp
from routes.dashboard import dashboard_bp
from routes.reports import reports_bp

def create_app(config_class=Config):
    app = Flask(__name__)
    app.config.from_object(config_class)

    # تهيئة قاعدة البيانات
    db.init_app(app)

    # إعداد الجلسة
    app.secret_key = app.config['SECRET_KEY']

    # دالة مساعدة للتحقق من تسجيل الدخول
    def is_logged_in():
        return 'user_id' in session

    # تسجيل المسارات
    app.register_blueprint(auth_bp)
    app.register_blueprint(cases_bp)
    app.register_blueprint(clients_bp)
    app.register_blueprint(appointments_bp)
    app.register_blueprint(invoices_bp)
    app.register_blueprint(documents_bp)
    app.register_blueprint(dashboard_bp)
    app.register_blueprint(reports_bp)

    # إنشاء مجلد التحميلات إذا لم يكن موجودًا
    os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)

    # مسار الصفحة الرئيسية
    @app.route('/')
    def index():
        if 'user_id' in session:
            return redirect(url_for('dashboard.index'))
        return redirect(url_for('auth.login'))

    # معالج الخطأ 404
    @app.errorhandler(404)
    def page_not_found(e):
        return render_template('errors/404.html'), 404

    # معالج الخطأ 500
    @app.errorhandler(500)
    def internal_server_error(e):
        return render_template('errors/500.html'), 500

    # دالة مساعدة للتحقق من امتدادات الملفات المسموح بها
    def allowed_file(filename):
        return '.' in filename and \
               filename.rsplit('.', 1)[1].lower() in app.config['ALLOWED_EXTENSIONS']

    # إضافة دوال مساعدة للقوالب
    @app.context_processor
    def utility_processor():
        return {
            'now': datetime.utcnow,
            'app_name': app.config['APP_NAME']
        }

    return app

if __name__ == '__main__':
    app = create_app()
    with app.app_context():
        db.create_all()  # إنشاء جداول قاعدة البيانات
    app.run(debug=True)
