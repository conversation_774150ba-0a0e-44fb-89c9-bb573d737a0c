{% extends "layout/base.html" %}

{% block title %}عرض العميل - {{ app_name }}{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>تفاصيل العميل: {{ client.first_name }} {{ client.last_name }}</h1>
    <div>
        <a href="{{ url_for('clients.edit_client', client_id=client.id) }}" class="btn btn-warning">
            <i class="fas fa-edit"></i> تعديل
        </a>
        <a href="{{ url_for('clients.index') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> العودة
        </a>
    </div>
</div>

<div class="row">
    <div class="col-md-4">
        <div class="card mb-4">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">معلومات العميل</h5>
            </div>
            <div class="card-body">
                <div class="text-center mb-4">
                    <img src="https://ui-avatars.com/api/?name={{ client.first_name }}+{{ client.last_name }}&size=128&background=random" alt="صورة العميل" class="rounded-circle">
                    <h4 class="mt-3">{{ client.first_name }} {{ client.last_name }}</h4>
                </div>
                <div class="mb-3">
                    <strong><i class="fas fa-id-card me-2"></i> رقم الهوية:</strong>
                    <p>{{ client.id_number or 'غير متوفر' }}</p>
                </div>
                <div class="mb-3">
                    <strong><i class="fas fa-envelope me-2"></i> البريد الإلكتروني:</strong>
                    <p>{{ client.email or 'غير متوفر' }}</p>
                </div>
                <div class="mb-3">
                    <strong><i class="fas fa-phone me-2"></i> رقم الهاتف:</strong>
                    <p>{{ client.phone }}</p>
                </div>
                <div class="mb-3">
                    <strong><i class="fas fa-map-marker-alt me-2"></i> العنوان:</strong>
                    <p>{{ client.address or 'غير متوفر' }}</p>
                </div>
                <div class="mb-3">
                    <strong><i class="fas fa-calendar-alt me-2"></i> تاريخ التسجيل:</strong>
                    <p>{{ client.created_at.strftime('%Y-%m-%d') }}</p>
                </div>
                {% if client.notes %}
                <div class="mb-3">
                    <strong><i class="fas fa-sticky-note me-2"></i> ملاحظات:</strong>
                    <p>{{ client.notes }}</p>
                </div>
                {% endif %}
            </div>
        </div>

        <!-- المستندات المرفقة -->
        <div class="card mb-4">
            <div class="card-header bg-info text-white d-flex justify-content-between align-items-center">
                <h5 class="mb-0">المستندات المرفقة</h5>
                <a href="{{ url_for('documents.add_document') }}?client_id={{ client.id }}" class="btn btn-sm btn-light">
                    <i class="fas fa-plus"></i> إضافة مستند
                </a>
            </div>
            <div class="card-body">
                {% if client.documents %}
                <div class="list-group">
                    {% for document in client.documents %}
                    <a href="{{ url_for('documents.view_document', document_id=document.id) }}" class="list-group-item list-group-item-action">
                        <div class="d-flex w-100 justify-content-between">
                            <h6 class="mb-1">{{ document.title }}</h6>
                            <small>{{ document.uploaded_at.strftime('%Y-%m-%d') }}</small>
                        </div>
                        <p class="mb-1">{{ document.document_type }}</p>
                    </a>
                    {% endfor %}
                </div>
                {% else %}
                <p class="text-muted">لا توجد مستندات مرفقة.</p>
                {% endif %}
            </div>
        </div>
    </div>

    <div class="col-md-8">
        <!-- قضايا العميل -->
        <div class="card mb-4">
            <div class="card-header bg-success text-white d-flex justify-content-between align-items-center">
                <h5 class="mb-0">قضايا العميل</h5>
                {% if session.role in ['admin', 'lawyer'] %}
                <a href="{{ url_for('cases.add_case') }}?client_id={{ client.id }}" class="btn btn-sm btn-light">
                    <i class="fas fa-plus"></i> إضافة قضية
                </a>
                {% endif %}
            </div>
            <div class="card-body">
                {% if cases %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>رقم القضية</th>
                                <th>العنوان</th>
                                <th>المحكمة</th>
                                <th>نوع القضية</th>
                                <th>تاريخ الفتح</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for case in cases %}
                            <tr>
                                <td>{{ case.case_number }}</td>
                                <td>{{ case.title }}</td>
                                <td>{{ case.court }}</td>
                                <td>{{ case.case_type }}</td>
                                <td>{{ case.opened_date.strftime('%Y-%m-%d') }}</td>
                                <td>
                                    {% if case.status == 'open' %}
                                    <span class="badge bg-success">مفتوحة</span>
                                    {% elif case.status == 'closed' %}
                                    <span class="badge bg-secondary">مغلقة</span>
                                    {% elif case.status == 'pending' %}
                                    <span class="badge bg-warning">معلقة</span>
                                    {% elif case.status == 'postponed' %}
                                    <span class="badge bg-info">مؤجلة</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <a href="{{ url_for('cases.view_case', case_id=case.id) }}" class="btn btn-sm btn-info" title="عرض">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <p class="text-muted">لا توجد قضايا مسجلة لهذا العميل.</p>
                {% endif %}
            </div>
        </div>

        <!-- فواتير العميل -->
        <div class="card mb-4">
            <div class="card-header bg-warning text-dark d-flex justify-content-between align-items-center">
                <h5 class="mb-0">فواتير العميل</h5>
                <a href="{{ url_for('invoices.add_invoice') }}?client_id={{ client.id }}" class="btn btn-sm btn-light">
                    <i class="fas fa-plus"></i> إضافة فاتورة
                </a>
            </div>
            <div class="card-body">
                {% if client.invoices %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>رقم الفاتورة</th>
                                <th>المبلغ</th>
                                <th>تاريخ الإصدار</th>
                                <th>تاريخ الاستحقاق</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for invoice in client.invoices %}
                            <tr>
                                <td>{{ invoice.invoice_number }}</td>
                                <td>{{ "%.2f"|format(invoice.amount) }}</td>
                                <td>{{ invoice.issue_date.strftime('%Y-%m-%d') }}</td>
                                <td>{{ invoice.due_date.strftime('%Y-%m-%d') }}</td>
                                <td>
                                    {% if invoice.status == 'paid' %}
                                    <span class="badge bg-success">مدفوعة</span>
                                    {% elif invoice.status == 'unpaid' %}
                                    <span class="badge bg-danger">غير مدفوعة</span>
                                    {% elif invoice.status == 'partial' %}
                                    <span class="badge bg-warning">مدفوعة جزئياً</span>
                                    {% elif invoice.status == 'cancelled' %}
                                    <span class="badge bg-secondary">ملغاة</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <a href="{{ url_for('invoices.view_invoice', invoice_id=invoice.id) }}" class="btn btn-sm btn-info" title="عرض">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <p class="text-muted">لا توجد فواتير مسجلة لهذا العميل.</p>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
