import os
from flask import Flask
from werkzeug.security import generate_password_hash
from datetime import datetime

from config import Config
from models import db, User

def create_app():
    app = Flask(__name__)
    app.config.from_object(Config)
    db.init_app(app)
    return app

def setup_database():
    """إعداد قاعدة البيانات وإنشاء مستخدم مسؤول"""
    app = create_app()
    
    with app.app_context():
        # إنشاء جداول قاعدة البيانات
        db.create_all()
        
        # التحقق من وجود مستخدم مسؤول
        admin = User.query.filter_by(username='admin').first()
        
        if not admin:
            # إنشاء مستخدم مسؤول
            admin_user = User(
                username='admin',
                email='<EMAIL>',
                first_name='مدير',
                last_name='النظام',
                role='admin',
                is_active=True,
                created_at=datetime.utcnow()
            )
            admin_user.set_password('admin123')
            
            db.session.add(admin_user)
            db.session.commit()
            
            print('تم إنشاء مستخدم مسؤول بنجاح:')
            print('اسم المستخدم: admin')
            print('كلمة المرور: admin123')
        else:
            print('مستخدم مسؤول موجود بالفعل')

if __name__ == '__main__':
    setup_database()
